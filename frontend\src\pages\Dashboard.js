import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Box,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Add as AddIcon,
  VideoLibrary as VideoLibraryIcon,
  PlayArrow as PlayIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const Dashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [stats, setStats] = useState(null);
  const [recentTasks, setRecentTasks] = useState([]);
  const [recentVideos, setRecentVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch generation stats
      const statsResponse = await axios.get('/api/v1/generation/stats');
      setStats(statsResponse.data);
      
      // Fetch recent generation tasks
      const tasksResponse = await axios.get('/api/v1/generation/tasks?limit=5');
      setRecentTasks(tasksResponse.data);
      
      // Fetch recent videos
      const videosResponse = await axios.get('/api/v1/videos/my?limit=5');
      setRecentVideos(videosResponse.data);
      
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon color="success" />;
      case 'processing':
        return <CircularProgress size={20} />;
      case 'queued':
        return <ScheduleIcon color="info" />;
      case 'failed':
        return <ErrorIcon color="error" />;
      default:
        return <ScheduleIcon />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'queued':
        return 'info';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Welcome back, {user?.full_name || user?.username}!
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Ready to create amazing videos with AI?
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ height: '100%', cursor: 'pointer' }} onClick={() => navigate('/generate')}>
            <CardContent sx={{ textAlign: 'center', py: 3 }}>
              <AddIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Generate New Video
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Create a new video using AI
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ height: '100%', cursor: 'pointer' }} onClick={() => navigate('/library')}>
            <CardContent sx={{ textAlign: 'center', py: 3 }}>
              <VideoLibraryIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Video Library
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Browse your created videos
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', py: 3 }}>
              <PlayIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {stats?.total_tasks || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Videos Generated
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Stats Overview */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Generation Statistics
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="h4" color="success.main">
                      {stats.completed_tasks}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completed
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="h4" color="warning.main">
                      {stats.active_tasks}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      In Progress
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Status Breakdown
                </Typography>
                {Object.entries(stats.by_status || {}).map(([status, count]) => (
                  <Box key={status} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                      {status}
                    </Typography>
                    <Chip 
                      label={count} 
                      size="small" 
                      color={getStatusColor(status)}
                    />
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Recent Activity */}
      <Grid container spacing={3}>
        {/* Recent Generation Tasks */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Generation Tasks
              </Typography>
              {recentTasks.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No generation tasks yet. Start by creating your first video!
                </Typography>
              ) : (
                recentTasks.map((task) => (
                  <Box key={task.id} sx={{ mb: 2, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      {getStatusIcon(task.status)}
                      <Typography variant="body2" sx={{ ml: 1, flexGrow: 1 }}>
                        {task.prompt.substring(0, 50)}...
                      </Typography>
                      <Chip 
                        label={task.status} 
                        size="small" 
                        color={getStatusColor(task.status)}
                      />
                    </Box>
                    {task.status === 'processing' && (
                      <LinearProgress 
                        variant="determinate" 
                        value={task.progress} 
                        sx={{ mt: 1 }}
                      />
                    )}
                  </Box>
                ))
              )}
              {recentTasks.length > 0 && (
                <Button 
                  variant="outlined" 
                  fullWidth 
                  onClick={() => navigate('/generate')}
                  sx={{ mt: 2 }}
                >
                  View All Tasks
                </Button>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Videos */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Videos
              </Typography>
              {recentVideos.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No videos yet. Generate your first video to get started!
                </Typography>
              ) : (
                recentVideos.map((video) => (
                  <Box 
                    key={video.id} 
                    sx={{ 
                      mb: 2, 
                      p: 2, 
                      border: '1px solid', 
                      borderColor: 'divider', 
                      borderRadius: 1,
                      cursor: 'pointer',
                      '&:hover': { backgroundColor: 'action.hover' }
                    }}
                    onClick={() => navigate(`/video/${video.id}`)}
                  >
                    <Typography variant="body1" gutterBottom>
                      {video.title}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(video.created_at).toLocaleDateString()}
                      </Typography>
                      <Chip 
                        label={video.status} 
                        size="small" 
                        color={getStatusColor(video.status)}
                      />
                    </Box>
                  </Box>
                ))
              )}
              {recentVideos.length > 0 && (
                <Button 
                  variant="outlined" 
                  fullWidth 
                  onClick={() => navigate('/library')}
                  sx={{ mt: 2 }}
                >
                  View All Videos
                </Button>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;
