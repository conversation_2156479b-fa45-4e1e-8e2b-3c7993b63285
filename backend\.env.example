# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/zylo_db

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# File Storage Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100000000  # 100MB

# AI Model Configuration
HUGGINGFACE_TOKEN=your-huggingface-token
MODEL_CACHE_DIR=./models

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Environment
ENVIRONMENT=development
DEBUG=True
