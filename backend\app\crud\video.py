"""
CRUD operations for Video model.
"""

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from typing import Optional, List
from datetime import datetime

from app.models.video import Video, VideoStatus
from app.schemas.video import VideoCreate, VideoUpdate


def get_video(db: Session, video_id: int) -> Optional[Video]:
    """Get video by ID."""
    return db.query(Video).filter(Video.id == video_id).first()


def get_video_by_filename(db: Session, filename: str) -> Optional[Video]:
    """Get video by filename."""
    return db.query(Video).filter(Video.filename == filename).first()


def get_videos(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    owner_id: Optional[int] = None,
    status: Optional[VideoStatus] = None,
    is_public: Optional[bool] = None,
    order_by: str = "created_at",
    order_desc: bool = True
) -> List[Video]:
    """Get multiple videos with filtering and pagination."""
    query = db.query(Video)
    
    # Apply filters
    if owner_id is not None:
        query = query.filter(Video.owner_id == owner_id)
    if status is not None:
        query = query.filter(Video.status == status)
    if is_public is not None:
        query = query.filter(Video.is_public == is_public)
    
    # Apply ordering
    if hasattr(Video, order_by):
        order_column = getattr(Video, order_by)
        if order_desc:
            query = query.order_by(desc(order_column))
        else:
            query = query.order_by(asc(order_column))
    
    return query.offset(skip).limit(limit).all()


def get_videos_count(
    db: Session,
    owner_id: Optional[int] = None,
    status: Optional[VideoStatus] = None,
    is_public: Optional[bool] = None
) -> int:
    """Get count of videos with filtering."""
    query = db.query(Video)
    
    # Apply filters
    if owner_id is not None:
        query = query.filter(Video.owner_id == owner_id)
    if status is not None:
        query = query.filter(Video.status == status)
    if is_public is not None:
        query = query.filter(Video.is_public == is_public)
    
    return query.count()


def get_public_videos(db: Session, skip: int = 0, limit: int = 100) -> List[Video]:
    """Get public videos."""
    return get_videos(
        db=db,
        skip=skip,
        limit=limit,
        is_public=True,
        status=VideoStatus.COMPLETED
    )


def get_user_videos(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Video]:
    """Get videos for a specific user."""
    return get_videos(
        db=db,
        skip=skip,
        limit=limit,
        owner_id=user_id
    )


def create_video(db: Session, video: VideoCreate, owner_id: int, **kwargs) -> Video:
    """Create a new video."""
    db_video = Video(
        title=video.title,
        description=video.description,
        is_public=video.is_public,
        owner_id=owner_id,
        **kwargs
    )
    db.add(db_video)
    db.commit()
    db.refresh(db_video)
    return db_video


def update_video(db: Session, video_id: int, video_update: VideoUpdate) -> Optional[Video]:
    """Update video information."""
    db_video = get_video(db, video_id)
    if not db_video:
        return None
    
    update_data = video_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_video, field, value)
    
    db_video.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_video)
    return db_video


def update_video_status(db: Session, video_id: int, status: VideoStatus) -> Optional[Video]:
    """Update video status."""
    db_video = get_video(db, video_id)
    if not db_video:
        return None
    
    db_video.status = status
    db_video.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_video)
    return db_video


def increment_view_count(db: Session, video_id: int) -> Optional[Video]:
    """Increment video view count."""
    db_video = get_video(db, video_id)
    if not db_video:
        return None
    
    db_video.view_count += 1
    db.commit()
    db.refresh(db_video)
    return db_video


def delete_video(db: Session, video_id: int) -> bool:
    """Delete a video."""
    db_video = get_video(db, video_id)
    if not db_video:
        return False
    
    db.delete(db_video)
    db.commit()
    return True
