"""
Video management endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os

from app.core.database import get_db
from app.core.config import settings
from app.crud.video import (
    get_video, get_videos, get_videos_count, get_user_videos, 
    get_public_videos, update_video, delete_video, increment_view_count
)
from app.schemas.video import Video, VideoUpdate, VideoList, VideoStatus
from app.schemas.user import User
from app.api.deps import get_current_user, get_optional_current_user

router = APIRouter()


@router.get("/", response_model=VideoList)
async def get_videos_list(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[VideoStatus] = None,
    public_only: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Get list of videos with pagination and filtering.
    
    Args:
        skip: Number of videos to skip
        limit: Maximum number of videos to return
        status: Filter by video status
        public_only: Show only public videos
        db: Database session
        current_user: Current authenticated user (optional)
        
    Returns:
        Paginated list of videos
    """
    if public_only or not current_user:
        # Show only public videos for unauthenticated users or when requested
        videos = get_public_videos(db=db, skip=skip, limit=limit)
        total = get_videos_count(db=db, is_public=True, status=VideoStatus.COMPLETED)
    else:
        # Show all videos for authenticated users (they can see their own private videos)
        videos = get_videos(
            db=db,
            skip=skip,
            limit=limit,
            status=status,
            owner_id=current_user.id if not current_user.is_superuser else None
        )
        total = get_videos_count(
            db=db,
            status=status,
            owner_id=current_user.id if not current_user.is_superuser else None
        )
    
    pages = (total + limit - 1) // limit
    
    return VideoList(
        videos=videos,
        total=total,
        page=(skip // limit) + 1,
        per_page=limit,
        pages=pages
    )


@router.get("/my", response_model=List[Video])
async def get_my_videos(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[VideoStatus] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get current user's videos.
    
    Args:
        skip: Number of videos to skip
        limit: Maximum number of videos to return
        status: Filter by video status
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of user's videos
    """
    videos = get_videos(
        db=db,
        skip=skip,
        limit=limit,
        owner_id=current_user.id,
        status=status
    )
    return videos


@router.get("/{video_id}", response_model=Video)
async def get_video_details(
    video_id: int,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Get video details by ID.
    
    Args:
        video_id: Video ID
        db: Database session
        current_user: Current authenticated user (optional)
        
    Returns:
        Video details
        
    Raises:
        HTTPException: If video not found or access denied
    """
    video = get_video(db=db, video_id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    # Check access permissions
    if not video.is_public:
        if not current_user or (video.owner_id != current_user.id and not current_user.is_superuser):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    # Increment view count for completed videos
    if video.status == VideoStatus.COMPLETED:
        increment_view_count(db=db, video_id=video_id)
    
    return video


@router.put("/{video_id}", response_model=Video)
async def update_video_details(
    video_id: int,
    video_update: VideoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update video details.
    
    Args:
        video_id: Video ID
        video_update: Video update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated video details
        
    Raises:
        HTTPException: If video not found or access denied
    """
    video = get_video(db=db, video_id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    # Check ownership
    if video.owner_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    updated_video = update_video(db=db, video_id=video_id, video_update=video_update)
    return updated_video


@router.delete("/{video_id}")
async def delete_video_by_id(
    video_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete video by ID.
    
    Args:
        video_id: Video ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If video not found or access denied
    """
    video = get_video(db=db, video_id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    # Check ownership
    if video.owner_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    # Delete video file if it exists
    if video.file_path and os.path.exists(video.file_path):
        try:
            os.remove(video.file_path)
        except OSError:
            pass  # File might already be deleted
    
    # Delete thumbnail if it exists
    if video.thumbnail_path and os.path.exists(video.thumbnail_path):
        try:
            os.remove(video.thumbnail_path)
        except OSError:
            pass
    
    success = delete_video(db=db, video_id=video_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete video"
        )
    
    return {"message": "Video deleted successfully"}


@router.get("/{video_id}/download")
async def download_video(
    video_id: int,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Download video file.
    
    Args:
        video_id: Video ID
        db: Database session
        current_user: Current authenticated user (optional)
        
    Returns:
        Video file
        
    Raises:
        HTTPException: If video not found, access denied, or file not available
    """
    video = get_video(db=db, video_id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    # Check access permissions
    if not video.is_public:
        if not current_user or (video.owner_id != current_user.id and not current_user.is_superuser):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    # Check if video is completed and file exists
    if video.status != VideoStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Video is not ready for download"
        )
    
    if not video.file_path or not os.path.exists(video.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video file not found"
        )
    
    return FileResponse(
        path=video.file_path,
        filename=video.filename,
        media_type="video/mp4"
    )
