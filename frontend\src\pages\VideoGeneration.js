import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Box,
  Alert,
  CircularProgress,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  PlayArrow as PlayIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const VideoGeneration = () => {
  const navigate = useNavigate();
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [formData, setFormData] = useState({
    prompt: '',
    negative_prompt: '',
    model_name: 'stable-video-diffusion',
    duration: 5.0,
    resolution: '1024x576',
    fps: 24,
    seed: '',
    guidance_scale: 7.5,
    num_inference_steps: 50
  });

  useEffect(() => {
    fetchAvailableModels();
  }, []);

  const fetchAvailableModels = async () => {
    try {
      const response = await axios.get('/api/v1/generation/models');
      setModels(response.data.models);
    } catch (err) {
      console.error('Error fetching models:', err);
    }
  };

  const handleChange = (field) => (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
    setSuccess('');
  };

  const handleSliderChange = (field) => (event, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Prepare request data
      const requestData = {
        ...formData,
        seed: formData.seed ? parseInt(formData.seed) : null
      };

      const response = await axios.post('/api/v1/generation/generate', requestData);
      
      setSuccess(`Video generation started! Task ID: ${response.data.task_id}`);
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
      
    } catch (err) {
      console.error('Error starting generation:', err);
      setError(err.response?.data?.detail || 'Failed to start video generation');
    } finally {
      setLoading(false);
    }
  };

  const selectedModel = models.find(m => m.name === formData.model_name);

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom>
        Generate Video
      </Typography>
      <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
        Create amazing videos using AI models
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Box component="form" onSubmit={handleSubmit}>
            {/* Basic Settings */}
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Prompt"
                  multiline
                  rows={3}
                  value={formData.prompt}
                  onChange={handleChange('prompt')}
                  placeholder="Describe the video you want to generate..."
                  required
                  disabled={loading}
                  helperText="Be descriptive and specific for better results"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Negative Prompt (Optional)"
                  multiline
                  rows={2}
                  value={formData.negative_prompt}
                  onChange={handleChange('negative_prompt')}
                  placeholder="What you don't want in the video..."
                  disabled={loading}
                  helperText="Specify what to avoid in the generated video"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>AI Model</InputLabel>
                  <Select
                    value={formData.model_name}
                    onChange={handleChange('model_name')}
                    disabled={loading}
                  >
                    {models.map((model) => (
                      <MenuItem key={model.name} value={model.name}>
                        {model.display_name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                {selectedModel && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {selectedModel.description}
                  </Typography>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Resolution</InputLabel>
                  <Select
                    value={formData.resolution}
                    onChange={handleChange('resolution')}
                    disabled={loading}
                  >
                    {selectedModel?.supported_resolutions?.map((res) => (
                      <MenuItem key={res} value={res}>
                        {res}
                      </MenuItem>
                    )) || (
                      <>
                        <MenuItem value="512x512">512x512</MenuItem>
                        <MenuItem value="768x768">768x768</MenuItem>
                        <MenuItem value="1024x576">1024x576</MenuItem>
                        <MenuItem value="1024x1024">1024x1024</MenuItem>
                      </>
                    )}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography gutterBottom>
                  Duration: {formData.duration} seconds
                </Typography>
                <Slider
                  value={formData.duration}
                  onChange={handleSliderChange('duration')}
                  min={1}
                  max={selectedModel?.max_duration || 60}
                  step={0.5}
                  marks={[
                    { value: 1, label: '1s' },
                    { value: 5, label: '5s' },
                    { value: 10, label: '10s' },
                    { value: 30, label: '30s' },
                    { value: 60, label: '60s' }
                  ]}
                  disabled={loading}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography gutterBottom>
                  FPS: {formData.fps}
                </Typography>
                <Slider
                  value={formData.fps}
                  onChange={handleSliderChange('fps')}
                  min={12}
                  max={60}
                  step={1}
                  marks={[
                    { value: 12, label: '12' },
                    { value: 24, label: '24' },
                    { value: 30, label: '30' },
                    { value: 60, label: '60' }
                  ]}
                  disabled={loading}
                />
              </Grid>
            </Grid>

            {/* Advanced Settings */}
            <Accordion sx={{ mt: 3 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SettingsIcon sx={{ mr: 1 }} />
                  <Typography>Advanced Settings</Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Seed (Optional)"
                      type="number"
                      value={formData.seed}
                      onChange={handleChange('seed')}
                      disabled={loading}
                      helperText="Leave empty for random seed"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>
                      Guidance Scale: {formData.guidance_scale}
                    </Typography>
                    <Slider
                      value={formData.guidance_scale}
                      onChange={handleSliderChange('guidance_scale')}
                      min={1}
                      max={20}
                      step={0.5}
                      disabled={loading}
                    />
                    <Typography variant="body2" color="text.secondary">
                      Higher values follow the prompt more closely
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>
                      Inference Steps: {formData.num_inference_steps}
                    </Typography>
                    <Slider
                      value={formData.num_inference_steps}
                      onChange={handleSliderChange('num_inference_steps')}
                      min={10}
                      max={100}
                      step={5}
                      disabled={loading}
                    />
                    <Typography variant="body2" color="text.secondary">
                      More steps = higher quality but slower generation
                    </Typography>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            {/* Submit Button */}
            <Box sx={{ mt: 4, textAlign: 'center' }}>
              <Button
                type="submit"
                variant="contained"
                size="large"
                startIcon={loading ? <CircularProgress size={20} /> : <PlayIcon />}
                disabled={loading || !formData.prompt.trim()}
                sx={{ minWidth: 200 }}
              >
                {loading ? 'Generating...' : 'Generate Video'}
              </Button>
            </Box>

            {/* Model Info */}
            {selectedModel && (
              <Box sx={{ mt: 3, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="h6" gutterBottom>
                  {selectedModel.display_name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {selectedModel.description}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label={`Max ${selectedModel.max_duration}s`} size="small" />
                  <Chip label={`Default: ${selectedModel.default_resolution}`} size="small" />
                  {selectedModel.supported_resolutions && (
                    <Chip 
                      label={`${selectedModel.supported_resolutions.length} resolutions`} 
                      size="small" 
                    />
                  )}
                </Box>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default VideoGeneration;
