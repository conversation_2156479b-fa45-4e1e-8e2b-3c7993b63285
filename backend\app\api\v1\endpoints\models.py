"""
AI model information endpoints.
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User

router = APIRouter()


@router.get("/models")
async def get_available_models(
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """
    Get list of available AI models for video generation.
    """
    models = [
        {
            "name": "stable-video-diffusion",
            "display_name": "Stable Video Diffusion",
            "description": "High-quality video generation from text prompts with excellent detail and coherence.",
            "max_duration": 60,
            "default_resolution": "1024x576",
            "supported_resolutions": [
                "512x512",
                "768x768", 
                "1024x576",
                "1024x1024"
            ],
            "supported_fps": [12, 24, 30],
            "default_fps": 24
        },
        {
            "name": "animatediff",
            "display_name": "AnimateDiff",
            "description": "Specialized in creating smooth animations and character movements.",
            "max_duration": 30,
            "default_resolution": "512x512",
            "supported_resolutions": [
                "512x512",
                "768x768"
            ],
            "supported_fps": [12, 24],
            "default_fps": 24
        }
    ]
    
    return {"models": models}


@router.get("/models/{model_name}")
async def get_model_info(
    model_name: str,
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """
    Get detailed information about a specific model.
    """
    models = {
        "stable-video-diffusion": {
            "name": "stable-video-diffusion",
            "display_name": "Stable Video Diffusion",
            "description": "High-quality video generation from text prompts with excellent detail and coherence.",
            "max_duration": 60,
            "default_resolution": "1024x576",
            "supported_resolutions": [
                "512x512",
                "768x768", 
                "1024x576",
                "1024x1024"
            ],
            "supported_fps": [12, 24, 30],
            "default_fps": 24,
            "parameters": {
                "guidance_scale": {
                    "min": 1.0,
                    "max": 20.0,
                    "default": 7.5,
                    "description": "How closely to follow the prompt"
                },
                "num_inference_steps": {
                    "min": 10,
                    "max": 100,
                    "default": 50,
                    "description": "Number of denoising steps"
                }
            }
        },
        "animatediff": {
            "name": "animatediff",
            "display_name": "AnimateDiff",
            "description": "Specialized in creating smooth animations and character movements.",
            "max_duration": 30,
            "default_resolution": "512x512",
            "supported_resolutions": [
                "512x512",
                "768x768"
            ],
            "supported_fps": [12, 24],
            "default_fps": 24,
            "parameters": {
                "guidance_scale": {
                    "min": 1.0,
                    "max": 15.0,
                    "default": 7.5,
                    "description": "How closely to follow the prompt"
                },
                "num_inference_steps": {
                    "min": 20,
                    "max": 80,
                    "default": 40,
                    "description": "Number of denoising steps"
                }
            }
        }
    }
    
    if model_name not in models:
        raise HTTPException(status_code=404, detail="Model not found")
    
    return models[model_name]
