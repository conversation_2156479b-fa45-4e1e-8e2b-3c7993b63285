"""
Video generation endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List
import uuid

from app.core.database import get_db
from app.crud.generation import (
    create_generation_task, get_generation_task, get_user_generation_tasks,
    get_active_tasks_count, update_task_status
)
from app.schemas.video import (
    VideoGenerationRequest, VideoGenerationResponse, VideoGenerationTask,
    TaskStatus
)
from app.schemas.user import User
from app.api.deps import get_current_user
from app.tasks.video_generation import generate_video_task

router = APIRouter()


@router.post("/generate", response_model=VideoGenerationResponse)
async def generate_video(
    request: VideoGenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Start video generation process.
    
    Args:
        request: Video generation parameters
        background_tasks: FastAPI background tasks
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Task information and estimated completion time
        
    Raises:
        HTTPException: If user has too many active tasks
    """
    # Check if user has too many active tasks
    active_tasks = get_active_tasks_count(db=db, user_id=current_user.id)
    max_concurrent_tasks = 3  # Limit per user
    
    if active_tasks >= max_concurrent_tasks:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Too many active tasks. Maximum {max_concurrent_tasks} concurrent tasks allowed."
        )
    
    # Generate unique task ID
    task_id = str(uuid.uuid4())
    
    # Create generation task record
    generation_task = create_generation_task(
        db=db,
        task_id=task_id,
        user_id=current_user.id,
        request=request
    )
    
    # Start video generation using Celery
    generate_video_task.delay(
        task_id=task_id,
        request_data=request.dict(),
        user_id=current_user.id
    )
    
    # Estimate completion time based on duration and complexity
    estimated_time = int(request.duration * 10)  # Rough estimate: 10 seconds per second of video
    
    return VideoGenerationResponse(
        task_id=task_id,
        message="Video generation started",
        estimated_time=estimated_time
    )


@router.get("/tasks", response_model=List[VideoGenerationTask])
async def get_my_generation_tasks(
    skip: int = 0,
    limit: int = 20,
    status: TaskStatus = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user's video generation tasks.
    
    Args:
        skip: Number of tasks to skip
        limit: Maximum number of tasks to return
        status: Filter by task status
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of user's generation tasks
    """
    tasks = get_user_generation_tasks(
        db=db,
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        status=status
    )
    return tasks


@router.get("/tasks/{task_id}", response_model=VideoGenerationTask)
async def get_generation_task_status(
    task_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get generation task status and progress.
    
    Args:
        task_id: Task ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Task status and details
        
    Raises:
        HTTPException: If task not found or access denied
    """
    task = get_generation_task(db=db, task_id=task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # Check ownership
    if task.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return task


@router.post("/tasks/{task_id}/cancel")
async def cancel_generation_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Cancel a video generation task.
    
    Args:
        task_id: Task ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If task not found, access denied, or cannot be cancelled
    """
    task = get_generation_task(db=db, task_id=task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # Check ownership
    if task.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    # Check if task can be cancelled
    if task.status not in [TaskStatus.QUEUED, TaskStatus.PROCESSING]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Task cannot be cancelled"
        )
    
    # Update task status to cancelled
    update_task_status(
        db=db,
        task_id=task_id,
        status=TaskStatus.CANCELLED
    )
    
    # TODO: Cancel the actual background task if using Celery
    # celery_app.control.revoke(task_id, terminate=True)
    
    return {"message": "Task cancelled successfully"}


@router.get("/models")
async def get_available_models():
    """
    Get list of available AI models for video generation.
    
    Returns:
        List of available models with their capabilities
    """
    models = [
        {
            "name": "stable-video-diffusion",
            "display_name": "Stable Video Diffusion",
            "description": "High-quality video generation from text prompts",
            "max_duration": 60,
            "supported_resolutions": ["512x512", "768x768", "1024x576", "1024x1024"],
            "default_resolution": "1024x576"
        },
        {
            "name": "animatediff",
            "display_name": "AnimateDiff",
            "description": "Animation-focused video generation",
            "max_duration": 30,
            "supported_resolutions": ["512x512", "768x768"],
            "default_resolution": "512x512"
        }
    ]
    return {"models": models}


@router.get("/stats")
async def get_generation_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user's video generation statistics.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        User's generation statistics
    """
    # Get user's tasks by status
    all_tasks = get_user_generation_tasks(db=db, user_id=current_user.id, limit=1000)
    
    stats = {
        "total_tasks": len(all_tasks),
        "by_status": {},
        "active_tasks": 0,
        "completed_tasks": 0,
        "failed_tasks": 0
    }
    
    for task in all_tasks:
        status_str = task.status.value
        stats["by_status"][status_str] = stats["by_status"].get(status_str, 0) + 1
        
        if task.status in [TaskStatus.QUEUED, TaskStatus.PROCESSING]:
            stats["active_tasks"] += 1
        elif task.status == TaskStatus.COMPLETED:
            stats["completed_tasks"] += 1
        elif task.status == TaskStatus.FAILED:
            stats["failed_tasks"] += 1
    
    return stats
