import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  Button,
  Box,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  IconButton
} from '@mui/material';
import {
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ArrowBack as ArrowBackIcon,
  PlayArrow as PlayIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';

const VideoDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [video, setVideo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchVideoDetails();
  }, [id]);

  const fetchVideoDetails = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/v1/videos/${id}`);
      setVideo(response.data);
    } catch (err) {
      console.error('Error fetching video details:', err);
      setError('Failed to load video details');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      const response = await axios.get(`/api/v1/videos/${id}/download`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', video.filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading video:', err);
      setError('Failed to download video');
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this video?')) {
      return;
    }

    try {
      await axios.delete(`/api/v1/videos/${id}`);
      navigate('/library');
    } catch (err) {
      console.error('Error deleting video:', err);
      setError('Failed to delete video');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error || !video) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Alert severity="error">
          {error || 'Video not found'}
        </Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/library')}
          sx={{ mt: 2 }}
        >
          Back to Library
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/library')}
          sx={{ mb: 2 }}
        >
          Back to Library
        </Button>
        <Typography variant="h3" component="h1" gutterBottom>
          {video.title}
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', flexWrap: 'wrap' }}>
          <Chip 
            label={video.status} 
            color={getStatusColor(video.status)}
          />
          {video.duration && (
            <Chip 
              label={formatDuration(video.duration)} 
              variant="outlined"
            />
          )}
          {video.resolution && (
            <Chip 
              label={video.resolution} 
              variant="outlined"
            />
          )}
          {video.format && (
            <Chip 
              label={video.format.toUpperCase()} 
              variant="outlined"
            />
          )}
        </Box>
      </Box>

      <Grid container spacing={4}>
        {/* Video Player */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              {video.status === 'completed' ? (
                <Box
                  sx={{
                    width: '100%',
                    height: 400,
                    backgroundColor: 'black',
                    borderRadius: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                  }}
                >
                  <video
                    controls
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain'
                    }}
                    poster={video.thumbnail_path ? `/api/v1/videos/${id}/thumbnail` : undefined}
                  >
                    <source src={`/api/v1/videos/${id}/download`} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </Box>
              ) : (
                <Box
                  sx={{
                    width: '100%',
                    height: 400,
                    backgroundColor: 'grey.200',
                    borderRadius: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'column'
                  }}
                >
                  {video.status === 'processing' ? (
                    <>
                      <CircularProgress sx={{ mb: 2 }} />
                      <Typography variant="h6" color="text.secondary">
                        Video is being generated...
                      </Typography>
                    </>
                  ) : video.status === 'failed' ? (
                    <>
                      <Typography variant="h6" color="error" sx={{ mb: 1 }}>
                        Generation Failed
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Please try generating the video again
                      </Typography>
                    </>
                  ) : (
                    <>
                      <PlayIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary">
                        Video Preview Not Available
                      </Typography>
                    </>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {video.status === 'completed' && (
              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                onClick={handleDownload}
              >
                Download
              </Button>
            )}
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDelete}
            >
              Delete
            </Button>
          </Box>
        </Grid>

        {/* Video Information */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Video Information
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Description
                </Typography>
                <Typography variant="body1">
                  {video.description || 'No description provided'}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Created
                </Typography>
                <Typography variant="body1">
                  {new Date(video.created_at).toLocaleString()}
                </Typography>
              </Box>

              {video.file_size && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    File Size
                  </Typography>
                  <Typography variant="body1">
                    {formatFileSize(video.file_size)}
                  </Typography>
                </Box>
              )}

              {video.prompt && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Generation Prompt
                    </Typography>
                    <Typography variant="body1" sx={{ fontStyle: 'italic' }}>
                      "{video.prompt}"
                    </Typography>
                  </Box>
                </>
              )}

              {video.negative_prompt && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Negative Prompt
                  </Typography>
                  <Typography variant="body1" sx={{ fontStyle: 'italic' }}>
                    "{video.negative_prompt}"
                  </Typography>
                </Box>
              )}

              {video.model_name && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    AI Model
                  </Typography>
                  <Typography variant="body1">
                    {video.model_name}
                  </Typography>
                </Box>
              )}

              {video.generation_params && Object.keys(video.generation_params).length > 0 && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Generation Parameters
                  </Typography>
                  {Object.entries(video.generation_params).map(([key, value]) => (
                    <Box key={key} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                        {key.replace(/_/g, ' ')}:
                      </Typography>
                      <Typography variant="body2">
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </Typography>
                    </Box>
                  ))}
                </>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default VideoDetails;
