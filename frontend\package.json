{"name": "zylo-frontend", "version": "1.0.0", "description": "AI-powered video generation frontend", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-dropzone": "^14.2.3", "react-player": "^2.13.0", "web-vitals": "^2.1.4", "socket.io-client": "^4.7.4", "date-fns": "^2.30.0", "formik": "^2.4.5", "yup": "^1.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17"}, "proxy": "http://localhost:8000"}