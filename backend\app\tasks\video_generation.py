"""
Celery tasks for video generation.
"""

import asyncio
from app.celery_app import celery_app
from app.services.video_generation import start_video_generation_task
from app.schemas.video import VideoGenerationRequest


@celery_app.task(bind=True)
def generate_video_task(self, task_id: str, request_data: dict, user_id: int):
    """
    Celery task for video generation.
    
    Args:
        task_id: Unique task identifier
        request_data: Video generation parameters
        user_id: User ID who requested the generation
    """
    try:
        # Convert dict back to Pydantic model
        request = VideoGenerationRequest(**request_data)
        
        # Run the async video generation function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            start_video_generation_task(task_id, request, user_id)
        )
        
        loop.close()
        return result
        
    except Exception as e:
        # Update task status to failed
        from app.core.database import SessionLocal
        from app.crud.generation import update_task_status
        from app.schemas.video import TaskStatus
        
        db = SessionLocal()
        try:
            update_task_status(
                db=db,
                task_id=task_id,
                status=TaskStatus.FAILED,
                error_message=str(e)
            )
        finally:
            db.close()
        
        raise
