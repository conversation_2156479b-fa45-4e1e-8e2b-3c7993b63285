"""
User management endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.core.database import get_db
from app.crud.user import get_user, get_users, update_user, delete_user
from app.schemas.user import User, UserUpdate
from app.api.deps import get_current_user, get_current_superuser

router = APIRouter()


@router.get("/me", response_model=User)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user profile.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user profile data
    """
    return current_user


@router.put("/me", response_model=User)
async def update_current_user_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update current user profile.
    
    Args:
        user_update: User update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Updated user profile data
    """
    updated_user = update_user(db=db, user_id=current_user.id, user_update=user_update)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return updated_user


@router.get("/{user_id}", response_model=User)
async def get_user_profile(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user profile by ID.
    
    Args:
        user_id: User ID to retrieve
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        User profile data
        
    Raises:
        HTTPException: If user not found
    """
    user = get_user(db=db, user_id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Users can only see their own profile or public profiles
    # For now, all profiles are visible to authenticated users
    return user


@router.get("/", response_model=List[User])
async def get_users_list(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_superuser)
):
    """
    Get list of users (admin only).
    
    Args:
        skip: Number of users to skip
        limit: Maximum number of users to return
        db: Database session
        current_user: Current authenticated superuser
        
    Returns:
        List of users
    """
    users = get_users(db=db, skip=skip, limit=limit)
    return users


@router.put("/{user_id}", response_model=User)
async def update_user_profile(
    user_id: int,
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_superuser)
):
    """
    Update user profile (admin only).
    
    Args:
        user_id: User ID to update
        user_update: User update data
        db: Database session
        current_user: Current authenticated superuser
        
    Returns:
        Updated user profile data
        
    Raises:
        HTTPException: If user not found
    """
    updated_user = update_user(db=db, user_id=user_id, user_update=user_update)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return updated_user


@router.delete("/{user_id}")
async def delete_user_account(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_superuser)
):
    """
    Delete user account (admin only).
    
    Args:
        user_id: User ID to delete
        db: Database session
        current_user: Current authenticated superuser
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If user not found
    """
    success = delete_user(db=db, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return {"message": "User deleted successfully"}
