# Setup Guide

This guide will help you set up the AI-powered video generation platform on your local machine.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 16 or higher)
- **Python** (version 3.9 or higher)
- **PostgreSQL** (version 12 or higher)
- **Redis** (for background task processing)
- **Git**

## Installation Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd zylo
```

### 2. Backend Setup

#### Create Virtual Environment
```bash
cd backend
python -m venv venv

# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate
```

#### Install Dependencies
```bash
pip install -r requirements.txt
```

#### Database Setup
1. Create a PostgreSQL database named `zylo_db`
2. Copy `.env.example` to `.env` and update the database credentials
3. Run database migrations:
```bash
alembic upgrade head
```

### 3. Frontend Setup

```bash
cd frontend
npm install
```

### 4. Environment Configuration

#### Backend Environment Variables
Copy `backend/.env.example` to `backend/.env` and configure:

```env
DATABASE_URL=postgresql://username:password@localhost:5432/zylo_db
SECRET_KEY=your-secret-key-here
REDIS_URL=redis://localhost:6379/0
HUGGINGFACE_TOKEN=your-huggingface-token
```

#### Frontend Environment Variables
Create `frontend/.env` with:

```env
REACT_APP_API_URL=http://localhost:8000
```

### 5. Running the Application

#### Start Backend Services
```bash
# Terminal 1 - Start Redis
redis-server

# Terminal 2 - Start Backend API
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Terminal 3 - Start Celery Worker
cd backend
celery -A app.celery_app worker --loglevel=info
```

#### Start Frontend
```bash
# Terminal 4 - Start Frontend
cd frontend
npm start
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Docker Setup (Alternative)

For easier setup, you can use Docker:

```bash
# Build and start all services
docker-compose up --build

# Run in background
docker-compose up -d
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check database credentials in `.env`
   - Verify database exists

2. **Redis Connection Error**
   - Ensure Redis server is running
   - Check Redis URL in `.env`

3. **Frontend Build Errors**
   - Clear node_modules: `rm -rf node_modules && npm install`
   - Check Node.js version compatibility

4. **Python Package Errors**
   - Ensure virtual environment is activated
   - Update pip: `pip install --upgrade pip`
   - Install packages individually if bulk install fails

### Getting Help

If you encounter issues:
1. Check the logs for error messages
2. Ensure all prerequisites are installed
3. Verify environment variables are set correctly
4. Check the GitHub issues page for known problems
