"""
Video generation service using AI models.
"""

import os
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import uuid
from datetime import datetime

from app.core.config import settings
from app.core.database import SessionLocal
from app.crud.generation import update_task_status, update_task_result
from app.crud.video import create_video
from app.schemas.video import VideoGenerationRequest, TaskStatus, VideoStatus, VideoCreate
from app.models.video import VideoGenerationTask

logger = logging.getLogger(__name__)


class VideoGenerationService:
    """Service for AI-powered video generation."""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.models_dir = Path(settings.MODEL_CACHE_DIR)
        
        # Ensure directories exist
        self.upload_dir.mkdir(exist_ok=True)
        (self.upload_dir / "videos").mkdir(exist_ok=True)
        (self.upload_dir / "thumbnails").mkdir(exist_ok=True)
        (self.upload_dir / "temp").mkdir(exist_ok=True)
        self.models_dir.mkdir(exist_ok=True)
    
    async def generate_video(
        self,
        task_id: str,
        request: VideoGenerationRequest,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Generate video using AI model.
        
        Args:
            task_id: Unique task identifier
            request: Video generation parameters
            user_id: User ID who requested the generation
            
        Returns:
            Generation result data
        """
        db = SessionLocal()
        
        try:
            # Update task status to processing
            update_task_status(
                db=db,
                task_id=task_id,
                status=TaskStatus.PROCESSING,
                progress=0.0
            )
            
            logger.info(f"Starting video generation for task {task_id}")
            
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"video_{timestamp}_{task_id[:8]}.mp4"
            video_path = self.upload_dir / "videos" / filename
            thumbnail_path = self.upload_dir / "thumbnails" / f"{filename.replace('.mp4', '.jpg')}"
            
            # Simulate video generation progress
            # In a real implementation, this would call actual AI models
            result = await self._simulate_video_generation(
                task_id=task_id,
                request=request,
                output_path=video_path,
                thumbnail_path=thumbnail_path,
                db=db
            )
            
            if result["success"]:
                # Create video record in database
                video_data = VideoCreate(
                    title=f"Generated Video - {request.prompt[:50]}...",
                    description=f"Generated using {request.model_name}",
                    is_public=False
                )
                
                video = create_video(
                    db=db,
                    video=video_data,
                    owner_id=user_id,
                    filename=filename,
                    file_path=str(video_path),
                    thumbnail_path=str(thumbnail_path),
                    duration=request.duration,
                    resolution=request.resolution,
                    format="mp4",
                    prompt=request.prompt,
                    negative_prompt=request.negative_prompt,
                    model_name=request.model_name,
                    generation_params=request.additional_params,
                    status=VideoStatus.COMPLETED,
                    file_size=result.get("file_size", 0),
                    generation_task_id=None  # Will be updated later
                )
                
                # Update task with success
                update_task_status(
                    db=db,
                    task_id=task_id,
                    status=TaskStatus.COMPLETED,
                    progress=100.0
                )
                
                update_task_result(
                    db=db,
                    task_id=task_id,
                    result_data={
                        "video_id": video.id,
                        "filename": filename,
                        "file_path": str(video_path),
                        "thumbnail_path": str(thumbnail_path),
                        "duration": request.duration,
                        "resolution": request.resolution
                    }
                )
                
                logger.info(f"Video generation completed for task {task_id}")
                return {"success": True, "video_id": video.id}
            
            else:
                # Update task with failure
                update_task_status(
                    db=db,
                    task_id=task_id,
                    status=TaskStatus.FAILED,
                    error_message=result.get("error", "Unknown error occurred")
                )
                
                logger.error(f"Video generation failed for task {task_id}: {result.get('error')}")
                return {"success": False, "error": result.get("error")}
        
        except Exception as e:
            logger.error(f"Error in video generation for task {task_id}: {str(e)}")
            
            # Update task with failure
            update_task_status(
                db=db,
                task_id=task_id,
                status=TaskStatus.FAILED,
                error_message=str(e)
            )
            
            return {"success": False, "error": str(e)}
        
        finally:
            db.close()
    
    async def _simulate_video_generation(
        self,
        task_id: str,
        request: VideoGenerationRequest,
        output_path: Path,
        thumbnail_path: Path,
        db
    ) -> Dict[str, Any]:
        """
        Simulate video generation process.
        In a real implementation, this would use actual AI models like Stable Video Diffusion.
        """
        try:
            # Simulate processing steps with progress updates
            steps = [
                ("Initializing model", 10),
                ("Processing prompt", 25),
                ("Generating frames", 60),
                ("Encoding video", 85),
                ("Creating thumbnail", 95),
                ("Finalizing", 100)
            ]
            
            for step_name, progress in steps:
                logger.info(f"Task {task_id}: {step_name}")
                
                # Update progress
                update_task_status(
                    db=db,
                    task_id=task_id,
                    status=TaskStatus.PROCESSING,
                    progress=float(progress)
                )
                
                # Simulate processing time
                await asyncio.sleep(2)
            
            # Create a dummy video file (in real implementation, this would be the generated video)
            await self._create_dummy_video(output_path, request.duration)
            
            # Create a dummy thumbnail
            await self._create_dummy_thumbnail(thumbnail_path)
            
            # Get file size
            file_size = output_path.stat().st_size if output_path.exists() else 0
            
            return {
                "success": True,
                "file_size": file_size,
                "output_path": str(output_path),
                "thumbnail_path": str(thumbnail_path)
            }
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _create_dummy_video(self, output_path: Path, duration: float):
        """Create a dummy video file for demonstration purposes."""
        try:
            # In a real implementation, this would use the AI model to generate the video
            # For now, we'll create a simple colored video using ffmpeg if available
            
            import subprocess
            
            # Try to create a simple test video using ffmpeg
            cmd = [
                "ffmpeg", "-y",
                "-f", "lavfi",
                "-i", f"color=c=blue:size=1024x576:duration={duration}",
                "-c:v", "libx264",
                "-pix_fmt", "yuv420p",
                str(output_path)
            ]
            
            try:
                subprocess.run(cmd, check=True, capture_output=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                # If ffmpeg is not available, create a placeholder file
                with open(output_path, "wb") as f:
                    # Write a minimal MP4 header (this won't be a valid video)
                    f.write(b"dummy video content for demonstration")
        
        except Exception as e:
            logger.warning(f"Could not create dummy video: {e}")
            # Create a placeholder file
            with open(output_path, "wb") as f:
                f.write(b"dummy video content")
    
    async def _create_dummy_thumbnail(self, thumbnail_path: Path):
        """Create a dummy thumbnail for demonstration purposes."""
        try:
            from PIL import Image
            
            # Create a simple colored thumbnail
            img = Image.new('RGB', (320, 180), color='blue')
            img.save(thumbnail_path, 'JPEG')
        
        except ImportError:
            # If PIL is not available, create a placeholder file
            with open(thumbnail_path, "wb") as f:
                f.write(b"dummy thumbnail content")


# Global service instance
video_generation_service = VideoGenerationService()


async def start_video_generation_task(
    task_id: str,
    request: VideoGenerationRequest,
    user_id: int
):
    """
    Start video generation task (called from FastAPI background tasks).
    
    Args:
        task_id: Unique task identifier
        request: Video generation parameters
        user_id: User ID who requested the generation
    """
    await video_generation_service.generate_video(task_id, request, user_id)
