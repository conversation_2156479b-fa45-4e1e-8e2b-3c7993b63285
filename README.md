# AI-Powered Video Generation Platform

A modern web application for AI-powered video generation with React frontend and Python backend.

## Project Structure

```
zylo/
├── frontend/          # React.js frontend application
├── backend/           # Python FastAPI backend
├── docs/             # Documentation
├── docker-compose.yml # Docker configuration
└── README.md         # This file
```

## Features

- **AI Video Generation**: Create videos using AI models
- **User Authentication**: Secure login and registration
- **Video Management**: Dashboard to manage created videos
- **Progress Tracking**: Real-time video generation progress
- **Responsive Design**: Modern, mobile-friendly interface
- **RESTful API**: Clean backend API architecture

## Tech Stack

### Frontend
- React.js 18+
- Material-UI / Tailwind CSS
- Axios for API calls
- React Router for navigation
- Context API for state management

### Backend
- Python 3.9+
- FastAPI framework
- SQLAlchemy ORM
- PostgreSQL database
- JWT authentication
- Celery for background tasks

## Features

### 🎬 AI Video Generation
- **Multiple AI Models**: Support for Stable Video Diffusion and AnimateDiff
- **Customizable Parameters**: Duration, resolution, FPS, guidance scale, and more
- **Real-time Progress Tracking**: Monitor generation progress with live updates
- **Background Processing**: Celery-powered async video generation

### 🔐 User Authentication
- **JWT-based Authentication**: Secure token-based auth with refresh tokens
- **User Registration & Login**: Complete user management system
- **Protected Routes**: Role-based access control for API endpoints

### 📚 Video Management
- **Video Library**: Browse, search, and filter your generated videos
- **Video Details**: Comprehensive video information and metadata
- **Download & Sharing**: Easy video download and sharing capabilities
- **Thumbnail Generation**: Automatic thumbnail creation for videos

### 🎨 Modern UI/UX
- **Material-UI Design**: Clean, responsive interface built with MUI
- **Dark/Light Theme**: Customizable theme support
- **Mobile Responsive**: Optimized for all device sizes
- **Intuitive Navigation**: User-friendly interface design

### ⚡ Performance & Scalability
- **Background Tasks**: Celery for handling long-running video generation
- **Database Optimization**: Efficient SQLAlchemy ORM with PostgreSQL
- **Caching**: Redis for session management and task queuing
- **Docker Support**: Containerized deployment for easy scaling

## Quick Start

### 🚀 Docker Compose (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd zylo
   ```

2. **Start with Docker Compose**
   ```bash
   docker-compose up --build
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### 🛠️ Manual Setup

#### Prerequisites
- Node.js 16+
- Python 3.9+
- PostgreSQL
- Redis (for Celery)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd zylo
```

2. Set up the backend:
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. Set up the frontend:
```bash
cd frontend
npm install
```

4. Configure environment variables (see docs/setup.md for details)

5. Run the application:
```bash
# Terminal 1 - Backend
cd backend
uvicorn main:app --reload

# Terminal 2 - Frontend
cd frontend
npm start

# Terminal 3 - Celery worker (for video generation)
cd backend
celery -A app.celery_app worker --loglevel=info
```

## API Documentation

The backend provides a comprehensive REST API with the following main endpoints:

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login-json` - User login
- `POST /api/v1/auth/refresh` - Token refresh
- `GET /api/v1/auth/me` - Get current user

### Video Generation
- `POST /api/v1/generation/generate` - Start video generation
- `GET /api/v1/generation/tasks` - List generation tasks
- `GET /api/v1/generation/tasks/{task_id}` - Get task status
- `DELETE /api/v1/generation/tasks/{task_id}` - Cancel task

### Video Management
- `GET /api/v1/videos/my` - List user's videos
- `GET /api/v1/videos/{video_id}` - Get video details
- `GET /api/v1/videos/{video_id}/download` - Download video
- `DELETE /api/v1/videos/{video_id}` - Delete video

### AI Models
- `GET /api/v1/generation/models` - List available AI models
- `GET /api/v1/generation/models/{model_name}` - Get model details

For complete API documentation, visit http://localhost:8000/docs when running the backend.

## Project Structure

```
zylo/
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── crud/           # Database operations
│   │   ├── models/         # SQLAlchemy models
│   │   ├── schemas/        # Pydantic schemas
│   │   ├── services/       # Business logic
│   │   └── tasks/          # Celery tasks
│   ├── requirements.txt    # Python dependencies
│   └── Dockerfile         # Backend Docker config
├── frontend/               # React.js frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # React contexts
│   │   ├── pages/          # Page components
│   │   └── utils/          # Utility functions
│   ├── package.json       # Node.js dependencies
│   └── Dockerfile         # Frontend Docker config
├── docker-compose.yml     # Multi-service setup
└── README.md              # This file
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

MIT License
