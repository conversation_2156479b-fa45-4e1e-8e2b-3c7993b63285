"""
Video and video generation task models.
"""

from sqlalchemy import <PERSON>umn, Integer, String, Boolean, DateTime, Text, ForeignKey, Float, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum

from app.core.database import Base


class VideoStatus(str, Enum):
    """Video processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskStatus(str, Enum):
    """Video generation task status."""
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Video(Base):
    """Video model for storing generated videos."""
    
    __tablename__ = "videos"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # File information
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=True)  # in bytes
    duration = Column(Float, nullable=True)  # in seconds
    resolution = Column(String(20), nullable=True)  # e.g., "1920x1080"
    format = Column(String(10), nullable=True)  # e.g., "mp4"
    
    # Thumbnail
    thumbnail_path = Column(String(500), nullable=True)
    
    # Generation parameters
    prompt = Column(Text, nullable=True)
    negative_prompt = Column(Text, nullable=True)
    model_name = Column(String(100), nullable=True)
    generation_params = Column(JSON, nullable=True)
    
    # Status and metadata
    status = Column(String(20), default=VideoStatus.PENDING)
    is_public = Column(Boolean, default=False)
    view_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Foreign keys
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    generation_task_id = Column(Integer, ForeignKey("video_generation_tasks.id"), nullable=True)
    
    # Relationships
    owner = relationship("User", back_populates="videos")
    generation_task = relationship("VideoGenerationTask", back_populates="video")
    
    def __repr__(self):
        return f"<Video(id={self.id}, title='{self.title}', status='{self.status}')>"


class VideoGenerationTask(Base):
    """Video generation task model for tracking background processing."""
    
    __tablename__ = "video_generation_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), unique=True, index=True, nullable=False)  # Celery task ID
    
    # Generation parameters
    prompt = Column(Text, nullable=False)
    negative_prompt = Column(Text, nullable=True)
    model_name = Column(String(100), nullable=False)
    duration = Column(Float, default=5.0)  # seconds
    resolution = Column(String(20), default="1024x576")
    fps = Column(Integer, default=24)
    seed = Column(Integer, nullable=True)
    guidance_scale = Column(Float, default=7.5)
    num_inference_steps = Column(Integer, default=50)
    
    # Additional parameters
    generation_params = Column(JSON, nullable=True)
    
    # Status and progress
    status = Column(String(20), default=TaskStatus.QUEUED)
    progress = Column(Float, default=0.0)  # 0.0 to 100.0
    error_message = Column(Text, nullable=True)
    
    # Results
    result_data = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="generation_tasks")
    video = relationship("Video", back_populates="generation_task", uselist=False)
    
    def __repr__(self):
        return f"<VideoGenerationTask(id={self.id}, task_id='{self.task_id}', status='{self.status}')>"
