"""
Main API router that includes all endpoint routers.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, videos, generation, models

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(videos.router, prefix="/videos", tags=["videos"])
api_router.include_router(generation.router, prefix="/generation", tags=["video-generation"])
api_router.include_router(models.router, prefix="/generation", tags=["models"])
