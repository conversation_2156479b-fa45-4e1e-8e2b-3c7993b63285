"""
CRUD operations for VideoGenerationTask model.
"""

from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.models.video import VideoGenerationTask, TaskStatus
from app.schemas.video import VideoGenerationRequest


def get_generation_task(db: Session, task_id: str) -> Optional[VideoGenerationTask]:
    """Get generation task by task ID."""
    return db.query(VideoGenerationTask).filter(VideoGenerationTask.task_id == task_id).first()


def get_generation_task_by_id(db: Session, id: int) -> Optional[VideoGenerationTask]:
    """Get generation task by database ID."""
    return db.query(VideoGenerationTask).filter(VideoGenerationTask.id == id).first()


def get_user_generation_tasks(
    db: Session,
    user_id: int,
    skip: int = 0,
    limit: int = 100,
    status: Optional[TaskStatus] = None
) -> List[VideoGenerationTask]:
    """Get generation tasks for a specific user."""
    query = db.query(VideoGenerationTask).filter(VideoGenerationTask.user_id == user_id)
    
    if status is not None:
        query = query.filter(VideoGenerationTask.status == status)
    
    return query.order_by(desc(VideoGenerationTask.created_at)).offset(skip).limit(limit).all()


def create_generation_task(
    db: Session,
    task_id: str,
    user_id: int,
    request: VideoGenerationRequest
) -> VideoGenerationTask:
    """Create a new video generation task."""
    db_task = VideoGenerationTask(
        task_id=task_id,
        user_id=user_id,
        prompt=request.prompt,
        negative_prompt=request.negative_prompt,
        model_name=request.model_name,
        duration=request.duration,
        resolution=request.resolution,
        fps=request.fps,
        seed=request.seed,
        guidance_scale=request.guidance_scale,
        num_inference_steps=request.num_inference_steps,
        generation_params=request.additional_params,
        status=TaskStatus.QUEUED
    )
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    return db_task


def update_generation_task(
    db: Session,
    task_id: str,
    **kwargs
) -> Optional[VideoGenerationTask]:
    """Update generation task."""
    db_task = get_generation_task(db, task_id)
    if not db_task:
        return None
    
    for field, value in kwargs.items():
        if hasattr(db_task, field):
            setattr(db_task, field, value)
    
    db_task.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_task)
    return db_task


def update_task_status(
    db: Session,
    task_id: str,
    status: TaskStatus,
    progress: Optional[float] = None,
    error_message: Optional[str] = None
) -> Optional[VideoGenerationTask]:
    """Update task status and progress."""
    db_task = get_generation_task(db, task_id)
    if not db_task:
        return None
    
    db_task.status = status
    if progress is not None:
        db_task.progress = progress
    if error_message is not None:
        db_task.error_message = error_message
    
    # Update timestamps based on status
    if status == TaskStatus.PROCESSING and db_task.started_at is None:
        db_task.started_at = datetime.utcnow()
    elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
        db_task.completed_at = datetime.utcnow()
        if status == TaskStatus.COMPLETED:
            db_task.progress = 100.0
    
    db_task.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_task)
    return db_task


def update_task_result(
    db: Session,
    task_id: str,
    result_data: Dict[str, Any]
) -> Optional[VideoGenerationTask]:
    """Update task result data."""
    db_task = get_generation_task(db, task_id)
    if not db_task:
        return None
    
    db_task.result_data = result_data
    db_task.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_task)
    return db_task


def delete_generation_task(db: Session, task_id: str) -> bool:
    """Delete a generation task."""
    db_task = get_generation_task(db, task_id)
    if not db_task:
        return False
    
    db.delete(db_task)
    db.commit()
    return True


def get_active_tasks_count(db: Session, user_id: Optional[int] = None) -> int:
    """Get count of active (queued or processing) tasks."""
    query = db.query(VideoGenerationTask).filter(
        VideoGenerationTask.status.in_([TaskStatus.QUEUED, TaskStatus.PROCESSING])
    )
    
    if user_id is not None:
        query = query.filter(VideoGenerationTask.user_id == user_id)
    
    return query.count()
