"""
Video-related Pydantic schemas.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum


class VideoStatus(str, Enum):
    """Video processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskStatus(str, Enum):
    """Video generation task status."""
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class VideoBase(BaseModel):
    """Base video schema."""
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    is_public: bool = False


class VideoCreate(VideoBase):
    """Schema for video creation."""
    pass


class VideoUpdate(BaseModel):
    """Schema for video updates."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    is_public: Optional[bool] = None


class VideoInDB(VideoBase):
    """Schema for video data from database."""
    id: int
    filename: str
    file_path: str
    file_size: Optional[int]
    duration: Optional[float]
    resolution: Optional[str]
    format: Optional[str]
    thumbnail_path: Optional[str]
    prompt: Optional[str]
    negative_prompt: Optional[str]
    model_name: Optional[str]
    generation_params: Optional[Dict[str, Any]]
    status: VideoStatus
    view_count: int
    created_at: datetime
    updated_at: Optional[datetime]
    owner_id: int
    generation_task_id: Optional[int]
    
    class Config:
        from_attributes = True


class Video(VideoInDB):
    """Public video schema."""
    pass


class VideoGenerationRequest(BaseModel):
    """Schema for video generation requests."""
    prompt: str = Field(..., min_length=1, max_length=1000)
    negative_prompt: Optional[str] = Field(None, max_length=1000)
    model_name: str = "stable-video-diffusion"
    duration: float = Field(default=5.0, ge=1.0, le=60.0)
    resolution: str = Field(default="1024x576", pattern=r"^\d+x\d+$")
    fps: int = Field(default=24, ge=1, le=60)
    seed: Optional[int] = Field(None, ge=0, le=2147483647)
    guidance_scale: float = Field(default=7.5, ge=1.0, le=20.0)
    num_inference_steps: int = Field(default=50, ge=10, le=100)
    additional_params: Optional[Dict[str, Any]] = None


class VideoGenerationTaskBase(BaseModel):
    """Base video generation task schema."""
    prompt: str
    negative_prompt: Optional[str]
    model_name: str
    duration: float
    resolution: str
    fps: int
    seed: Optional[int]
    guidance_scale: float
    num_inference_steps: int


class VideoGenerationTaskInDB(VideoGenerationTaskBase):
    """Schema for video generation task from database."""
    id: int
    task_id: str
    status: TaskStatus
    progress: float
    error_message: Optional[str]
    result_data: Optional[Dict[str, Any]]
    generation_params: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: Optional[datetime]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    user_id: int
    
    class Config:
        from_attributes = True


class VideoGenerationTask(VideoGenerationTaskInDB):
    """Public video generation task schema."""
    pass


class VideoGenerationResponse(BaseModel):
    """Schema for video generation response."""
    task_id: str
    message: str
    estimated_time: Optional[int] = None  # in seconds


class VideoList(BaseModel):
    """Schema for paginated video list."""
    videos: list[Video]
    total: int
    page: int
    per_page: int
    pages: int


class VideoStats(BaseModel):
    """Schema for video statistics."""
    total_videos: int
    total_duration: float
    total_size: int
    by_status: Dict[str, int]
    by_model: Dict[str, int]
